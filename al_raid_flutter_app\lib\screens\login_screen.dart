import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'dart:math' as math;
import '../providers/auth_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/language_provider.dart';
import '../widgets/glassmorphic_card.dart';
import '../widgets/gradient_button.dart';
import '../widgets/animated_input_field.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  late AnimationController _mainAnimationController;
  late AnimationController _backgroundAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isPasswordVisible = false;
  bool _rememberMe = false;

  @override
  void initState() {
    super.initState();

    _mainAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _backgroundAnimationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _mainAnimationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _mainAnimationController,
        curve: const Interval(0.3, 1.0, curve: Curves.elasticOut),
      ),
    );

    _mainAnimationController.forward();
  }

  @override
  void dispose() {
    _mainAnimationController.dispose();
    _backgroundAnimationController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    if (_formKey.currentState?.validate() ?? false) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final languageProvider =
          Provider.of<LanguageProvider>(context, listen: false);
      final isRTL = languageProvider.isArabic;

      final success = await authProvider.login(
        _emailController.text.trim(),
        _passwordController.text,
      );

      if (success && authProvider.isAuthenticated && mounted) {
        context.go('/main');
      } else if (!success && mounted) {
        // Show error message from AuthProvider
        final errorMessage = authProvider.error ??
            (isRTL ? 'فشل في تسجيل الدخول' : 'Login failed');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final size = MediaQuery.of(context).size;
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isRTL = languageProvider.isArabic;

    return Scaffold(
      body: Stack(
        children: [
          // Animated Background
          AnimatedBuilder(
            animation: _backgroundAnimationController,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    transform: GradientRotation(
                      _backgroundAnimationController.value * 2 * 3.14159,
                    ),
                    colors: isDark
                        ? [
                            const Color(0xFF1A1A2E),
                            const Color(0xFF16213E),
                            const Color(0xFF0F4C75),
                          ]
                        : [
                            const Color(0xFFE3F2FD),
                            const Color(0xFFBBDEFB),
                            const Color(0xFF90CAF9),
                          ],
                  ),
                ),
              );
            },
          ),

          // Floating Elements
          Positioned(
            top: size.height * 0.1,
            right: size.width * 0.8,
            child: _buildFloatingElement(60, Colors.white.withOpacity(0.1)),
          ),
          Positioned(
            top: size.height * 0.3,
            left: size.width * 0.1,
            child: _buildFloatingElement(40, Colors.white.withOpacity(0.05)),
          ),
          Positioned(
            bottom: size.height * 0.2,
            right: size.width * 0.1,
            child: _buildFloatingElement(80, Colors.white.withOpacity(0.08)),
          ),

          // Main Content
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
              child: AnimatedBuilder(
                animation: _mainAnimationController,
                builder: (context, child) {
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Language Toggle
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              IconButton(
                                onPressed: () {
                                  Provider.of<ThemeProvider>(
                                    context,
                                    listen: false,
                                  ).toggleTheme();
                                },
                                icon: Icon(
                                  isDark ? Icons.light_mode : Icons.dark_mode,
                                  color: Colors.white70,
                                ),
                              ),
                              TextButton(
                                onPressed: () {
                                  languageProvider.toggleLanguage();
                                },
                                child: Text(
                                  isRTL ? 'English' : 'العربية',
                                  style: const TextStyle(
                                    color: Colors.white70,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          SizedBox(height: size.height * 0.08),

                          // Logo and Title
                          Center(
                            child: Column(
                              children: [
                                Container(
                                  width: 120,
                                  height: 120,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: LinearGradient(
                                      colors: [
                                        theme.primaryColor,
                                        theme.primaryColor.withOpacity(0.7),
                                      ],
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color:
                                            theme.primaryColor.withOpacity(0.3),
                                        blurRadius: 20,
                                        offset: const Offset(0, 10),
                                      ),
                                    ],
                                  ),
                                  child: const Icon(
                                    Icons.store,
                                    size: 60,
                                    color: Colors.white,
                                  ),
                                )
                                    .animate()
                                    .scale(
                                      duration: 800.ms,
                                      curve: Curves.elasticOut,
                                    )
                                    .shimmer(delay: 1000.ms, duration: 1500.ms),
                                const SizedBox(height: 24),
                                Text(
                                  isRTL ? 'مرحباً بعودتك' : 'Welcome Back',
                                  style: TextStyle(
                                    fontSize: 32,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    shadows: [
                                      Shadow(
                                        color: Colors.black.withOpacity(
                                          0.3,
                                        ),
                                        blurRadius: 10,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                )
                                    .animate()
                                    .fadeIn(delay: 400.ms, duration: 800.ms)
                                    .slideY(begin: 0.3, end: 0),
                                const SizedBox(height: 8),
                                Text(
                                  isRTL
                                      ? 'سجل دخولك للمتابعة'
                                      : 'Sign in to continue',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.white70,
                                    shadows: [
                                      Shadow(
                                        color: Colors.black.withOpacity(0.3),
                                        blurRadius: 5,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                ).animate().fadeIn(
                                      delay: 600.ms,
                                      duration: 800.ms,
                                    ),
                              ],
                            ),
                          ),

                          SizedBox(height: size.height * 0.06),

                          // Login Form
                          GlassmorphicCard(
                            child: Form(
                              key: _formKey,
                              child: Column(
                                children: [
                                  AnimatedInputField(
                                    labelText:
                                        isRTL ? 'البريد الإلكتروني' : 'Email',
                                    hintText: isRTL
                                        ? 'أدخل بريدك الإلكتروني'
                                        : 'Enter your email',
                                    controller: _emailController,
                                    keyboardType: TextInputType.emailAddress,
                                    prefixIcon: const Icon(
                                      Icons.email_outlined,
                                    ),
                                    validator: (value) {
                                      if (value?.isEmpty ?? true) {
                                        return isRTL
                                            ? 'يرجى إدخال البريد الإلكتروني'
                                            : 'Please enter your email';
                                      }
                                      if (!RegExp(
                                        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                      ).hasMatch(value!)) {
                                        return isRTL
                                            ? 'يرجى إدخال بريد إلكتروني صحيح'
                                            : 'Please enter a valid email';
                                      }
                                      return null;
                                    },
                                    isRequired: true,
                                  ),

                                  AnimatedInputField(
                                    labelText:
                                        isRTL ? 'كلمة المرور' : 'Password',
                                    hintText: isRTL
                                        ? 'أدخل كلمة المرور'
                                        : 'Enter your password',
                                    controller: _passwordController,
                                    obscureText: !_isPasswordVisible,
                                    prefixIcon: const Icon(
                                      Icons.lock_outline,
                                    ),
                                    suffixIcon: IconButton(
                                      icon: Icon(
                                        _isPasswordVisible
                                            ? Icons.visibility_off
                                            : Icons.visibility,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isPasswordVisible =
                                              !_isPasswordVisible;
                                        });
                                      },
                                    ),
                                    validator: (value) {
                                      if (value?.isEmpty ?? true) {
                                        return isRTL
                                            ? 'يرجى إدخال كلمة المرور'
                                            : 'Please enter your password';
                                      }
                                      if (value!.length < 6) {
                                        return isRTL
                                            ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
                                            : 'Password must be at least 6 characters';
                                      }
                                      return null;
                                    },
                                    isRequired: true,
                                  ),

                                  // Remember Me & Forgot Password
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Checkbox(
                                            value: _rememberMe,
                                            onChanged: (value) {
                                              setState(() {
                                                _rememberMe = value ?? false;
                                              });
                                            },
                                            activeColor: theme.primaryColor,
                                          ),
                                          Text(
                                            isRTL ? 'تذكرني' : 'Remember me',
                                            style: TextStyle(
                                              color: isDark
                                                  ? Colors.white70
                                                  : Colors.grey.shade700,
                                            ),
                                          ),
                                        ],
                                      ),
                                      TextButton(
                                        onPressed: () {
                                          // TODO: Implement forgot password
                                        },
                                        child: Text(
                                          isRTL
                                              ? 'نسيت كلمة المرور؟'
                                              : 'Forgot Password?',
                                          style: TextStyle(
                                            color: theme.primaryColor,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 24),

                                  // Login Button
                                  Consumer<AuthProvider>(
                                    builder: (
                                      context,
                                      authProvider,
                                      child,
                                    ) {
                                      return GradientButton(
                                        text:
                                            isRTL ? 'تسجيل الدخول' : 'Sign In',
                                        onPressed: _handleLogin,
                                        isLoading: authProvider.isLoading,
                                        width: double.infinity,
                                        icon: const Icon(
                                          Icons.login,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),
                          )
                              .animate()
                              .fadeIn(delay: 800.ms, duration: 800.ms)
                              .slideY(begin: 0.2, end: 0),

                          const SizedBox(height: 32),

                          // Sign Up Link
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                isRTL
                                    ? 'ليس لديك حساب؟ '
                                    : "Don't have an account? ",
                                style: TextStyle(
                                  color: Colors.white70,
                                  fontSize: 16,
                                ),
                              ),
                              TextButton(
                                onPressed: () {
                                  context.push('/register');
                                },
                                child: Text(
                                  isRTL ? 'إنشاء حساب' : 'Sign Up',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ).animate().fadeIn(delay: 1000.ms, duration: 800.ms),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingElement(double size, Color color) {
    return AnimatedBuilder(
      animation: _backgroundAnimationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            20 * math.sin(_backgroundAnimationController.value * 2 * 3.14159),
            20 * math.cos(_backgroundAnimationController.value * 2 * 3.14159),
          ),
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(shape: BoxShape.circle, color: color),
          ),
        );
      },
    );
  }
}
